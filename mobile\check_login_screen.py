#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录页面错误检查脚本
检查login_screen.py中的潜在问题
"""

import os
import sys
import re
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_kv_bindings():
    """检查KV字符串中的事件绑定"""
    logger.info("检查KV字符串中的事件绑定...")
    
    try:
        with open('screens/login_screen.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取KV字符串
        kv_match = re.search(r"KV = '''(.*?)'''", content, re.DOTALL)
        if not kv_match:
            logger.error("未找到KV字符串")
            return False
        
        kv_content = kv_match.group(1)
        
        # 检查事件绑定
        bindings = re.findall(r'on_release:\s*(.+)', kv_content)
        
        logger.info(f"找到 {len(bindings)} 个事件绑定:")
        for i, binding in enumerate(bindings, 1):
            logger.info(f"  {i}. {binding.strip()}")
        
        # 检查是否有错误的绑定模式
        error_patterns = [
            r'root\.parent\.parent\.',
            r'root\.parent\.',
        ]
        
        errors = []
        for pattern in error_patterns:
            matches = re.findall(pattern, kv_content)
            if matches:
                errors.extend(matches)
        
        if errors:
            logger.error(f"发现 {len(errors)} 个可能的错误绑定:")
            for error in errors:
                logger.error(f"  - {error}")
            return False
        else:
            logger.info("KV事件绑定检查通过")
            return True
            
    except Exception as e:
        logger.error(f"检查KV绑定时出错: {e}")
        return False

def check_required_methods():
    """检查必需的方法是否存在"""
    logger.info("检查必需的方法...")
    
    required_methods = [
        'on_login',
        'on_request_verification_code', 
        'show_identity_menu',
        'switch_tab',
        'on_wechat_login',
        'on_fingerprint_login',
        'on_face_login',
        'on_register',
        'show_error',
        'show_success',
        'show_info'
    ]
    
    try:
        with open('screens/login_screen.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_methods = []
        for method in required_methods:
            pattern = rf'def {method}\s*\('
            if not re.search(pattern, content):
                missing_methods.append(method)
        
        if missing_methods:
            logger.error(f"缺少以下方法: {missing_methods}")
            return False
        else:
            logger.info("所有必需方法都存在")
            return True
            
    except Exception as e:
        logger.error(f"检查方法时出错: {e}")
        return False

def check_class_structure():
    """检查类结构"""
    logger.info("检查类结构...")
    
    try:
        # 尝试导入模块
        import screens.login_screen as login_module
        
        # 检查类是否存在
        required_classes = ['LoginScreen', 'LoginTab', 'PhoneTab', 'LoginForm']
        missing_classes = []
        
        for cls_name in required_classes:
            if not hasattr(login_module, cls_name):
                missing_classes.append(cls_name)
        
        if missing_classes:
            logger.error(f"缺少以下类: {missing_classes}")
            return False
        else:
            logger.info("所有必需类都存在")
            return True
            
    except Exception as e:
        logger.error(f"检查类结构时出错: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始检查登录页面...")
    
    checks = [
        ("KV事件绑定", check_kv_bindings),
        ("必需方法", check_required_methods),
        ("类结构", check_class_structure),
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        logger.info(f"\n{'='*50}")
        logger.info(f"检查: {check_name}")
        logger.info(f"{'='*50}")
        
        if not check_func():
            all_passed = False
            logger.error(f"{check_name} 检查失败")
        else:
            logger.info(f"{check_name} 检查通过")
    
    logger.info(f"\n{'='*50}")
    if all_passed:
        logger.info("所有检查都通过！登录页面应该可以正常工作。")
        return 0
    else:
        logger.error("发现问题，需要修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
