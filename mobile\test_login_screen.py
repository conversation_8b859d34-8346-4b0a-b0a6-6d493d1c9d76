#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录页面测试脚本
用于验证修改后的登录页面是否正常工作
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from kivymd.app import MDApp
    from kivy.core.window import Window
    from kivy.uix.screenmanager import ScreenManager
    from mobile.screens.login_screen import LoginScreen
    from mobile.theme import AppTheme
    
    class TestLoginApp(MDApp):
        """测试登录页面的应用"""
        
        def __init__(self, **kwargs):
            super().__init__(**kwargs)
            self.title = "登录页面测试"
            
        def build(self):
            """构建应用界面"""
            try:
                # 设置主题
                self.theme_cls.theme_style = "Light"
                self.theme_cls.primary_palette = "Blue"
                
                # 设置窗口大小（模拟手机屏幕）
                Window.size = (360, 640)
                
                # 创建屏幕管理器
                sm = ScreenManager()
                
                # 创建登录屏幕
                login_screen = LoginScreen(name="login_screen")
                sm.add_widget(login_screen)
                
                # 设置当前屏幕
                sm.current = "login_screen"
                
                logger.info("登录页面测试应用构建完成")
                return sm
                
            except Exception as e:
                logger.error(f"构建应用失败: {e}")
                import traceback
                traceback.print_exc()
                return None
        
        def on_start(self):
            """应用启动时的处理"""
            logger.info("登录页面测试应用启动")
            
        def on_stop(self):
            """应用停止时的处理"""
            logger.info("登录页面测试应用停止")

    def main():
        """主函数"""
        try:
            logger.info("开始测试登录页面...")
            
            # 创建并运行测试应用
            app = TestLoginApp()
            app.run()
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
            import traceback
            traceback.print_exc()
            return 1
            
        return 0

    if __name__ == "__main__":
        sys.exit(main())
        
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所需的依赖包：")
    print("pip install kivymd kivy")
    sys.exit(1)
